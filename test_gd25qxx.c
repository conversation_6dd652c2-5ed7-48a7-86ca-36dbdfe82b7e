#include "Components/gd25qxx/gd25qxx.h"

/**
 * @brief 测试GD25QXX SPI Flash功能
 * @note 此函数演示如何使用适配后的GD25QXX库
 */
void test_gd25qxx_hal(void)
{
    uint32_t flash_id;
    uint8_t test_data[256];
    uint8_t read_data[256];
    uint32_t test_address = 0x1000; // 测试地址
    
    // 1. 初始化SPI Flash
    spi_flash_init();
    
    // 2. 读取Flash ID
    flash_id = spi_flash_read_id();
    // 常见的GD25Q系列ID:
    // GD25Q16: 0xC84015
    // GD25Q32: 0xC84016  
    // GD25Q64: 0xC84017
    // GD25Q128: 0xC84018
    
    if ((flash_id & 0xFFFF00) == 0xC84000) {
        // 检测到GD25Q系列Flash
        
        // 3. 擦除测试扇区
        spi_flash_sector_erase(test_address);
        
        // 4. 准备测试数据
        for (int i = 0; i < 256; i++) {
            test_data[i] = i;
        }
        
        // 5. 写入数据
        spi_flash_page_write(test_data, test_address, 256);
        
        // 6. 读取数据
        spi_flash_buffer_read(read_data, test_address, 256);
        
        // 7. 验证数据
        int verify_ok = 1;
        for (int i = 0; i < 256; i++) {
            if (test_data[i] != read_data[i]) {
                verify_ok = 0;
                break;
            }
        }
        
        if (verify_ok) {
            // 测试成功
        } else {
            // 测试失败
        }
    }
}

/**
 * @brief 批量数据读写测试
 */
void test_gd25qxx_bulk_rw(void)
{
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t test_addr = 0x2000;
    
    // 初始化
    spi_flash_init();
    
    // 准备测试数据
    for (int i = 0; i < SPI_FLASH_PAGE_SIZE; i++) {
        write_buffer[i] = (uint8_t)(i ^ 0xAA);
    }
    
    // 擦除扇区
    spi_flash_sector_erase(test_addr);
    
    // 写入整页数据
    spi_flash_buffer_write(write_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    
    // 读取数据
    spi_flash_buffer_read(read_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    
    // 验证数据一致性
    // 可以在此处添加验证逻辑
}
