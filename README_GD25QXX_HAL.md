# GD25QXX SPI Flash STM32F407 HAL库适配

## 概述
本项目将原本基于GD32F4xx标准库的GD25QXX SPI Flash驱动适配为STM32F407 HAL库版本。

## 主要修改内容

### 1. 头文件适配
- 将 `gd32f4xx.h`、`gd32f4xx_spi.h`、`gd32f4xx_gpio.h` 替换为 `main.h`、`spi.h`、`gpio.h`
- 更新SPI外设定义，使用HAL库的句柄方式

### 2. GPIO操作适配
- 将GD32的GPIO操作函数替换为HAL库函数
- CS引脚使用PA4（根据用户要求）
- 宏定义：
  ```c
  #define SPI_FLASH_CS_LOW()  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET)
  #define SPI_FLASH_CS_HIGH() HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_SET)
  ```

### 3. SPI传输适配
- 移除复杂的DMA配置代码
- 使用HAL库的 `HAL_SPI_TransmitReceive()` 函数
- 简化传输逻辑，提高代码可读性

### 4. 函数接口保持兼容
保持原有的API接口不变：
- `spi_flash_init()` - 初始化SPI Flash
- `spi_flash_read_id()` - 读取Flash ID
- `spi_flash_sector_erase()` - 扇区擦除
- `spi_flash_bulk_erase()` - 整片擦除
- `spi_flash_page_write()` - 页写入
- `spi_flash_buffer_write()` - 缓冲区写入
- `spi_flash_buffer_read()` - 缓冲区读取

## 硬件连接

### SPI1连接
- PA5 -> SPI1_SCK (时钟)
- PA6 -> SPI1_MISO (主入从出)
- PA7 -> SPI1_MOSI (主出从入)
- PA4 -> CS (片选，软件控制)

### 支持的Flash型号
- GD25Q16 (ID: 0xC84015)
- GD25Q32 (ID: 0xC84016)
- GD25Q64 (ID: 0xC84017)
- GD25Q128 (ID: 0xC84018)

## 使用方法

### 1. 基本初始化
```c
#include "Components/gd25qxx/gd25qxx.h"

// 初始化SPI Flash
spi_flash_init();

// 读取Flash ID
uint32_t flash_id = spi_flash_read_id();
```

### 2. 数据读写
```c
uint8_t write_data[256];
uint8_t read_data[256];
uint32_t address = 0x1000;

// 准备数据
for(int i = 0; i < 256; i++) {
    write_data[i] = i;
}

// 擦除扇区
spi_flash_sector_erase(address);

// 写入数据
spi_flash_page_write(write_data, address, 256);

// 读取数据
spi_flash_buffer_read(read_data, address, 256);
```

### 3. 测试函数
提供了 `test_spi_flash()` 函数用于基本功能测试。

## 配置要求

### CubeMX配置
1. 启用SPI1外设，配置为Master模式
2. 配置PA4为GPIO输出（用作CS引脚）
3. SPI参数建议：
   - 数据大小：8位
   - 时钟极性：低电平
   - 时钟相位：第一边沿
   - 波特率分频：8或16

### 编译配置
确保项目包含路径中添加了 `Components/gd25qxx` 目录。

## 性能优化

### 相比原GD32版本的改进
1. **代码简化**：移除复杂的DMA配置，使用HAL库统一接口
2. **可读性提升**：代码结构更清晰，注释更完善
3. **移植性增强**：基于HAL库，便于移植到其他STM32系列
4. **错误处理**：增加了传输错误检查机制

### 注意事项
1. 确保SPI时钟频率不超过Flash芯片规格
2. 写入操作前必须先擦除对应扇区
3. 页写入不能跨越页边界（256字节对齐）
4. 擦除和写入操作需要等待完成

## 测试验证
项目包含测试文件 `test_gd25qxx.c`，提供了完整的功能测试示例。

## 版本信息
- 适配版本：STM32F407 HAL库版本
- 原始版本：基于GD32F4xx标准库
- 适配日期：2025年1月
